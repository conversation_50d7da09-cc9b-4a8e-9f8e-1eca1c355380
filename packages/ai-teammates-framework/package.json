{"name": "@brainstack/ai-teammates-framework", "version": "0.1.0", "description": "Functional AI Teammates Framework for building AI agents with tools, resources, and prompts - Part of the AI-SDLC experimental framework", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./core": {"import": "./dist/core/index.js", "types": "./dist/core/index.d.ts"}, "./plugins": {"import": "./dist/plugins/index.js", "types": "./dist/plugins/index.d.ts"}, "./shared": {"import": "./dist/shared/index.js", "types": "./dist/shared/index.d.ts"}}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "test": "NODE_OPTIONS='--experimental-vm-modules' jest", "test:watch": "NODE_OPTIONS='--experimental-vm-modules' jest --watch", "test:coverage": "NODE_OPTIONS='--experimental-vm-modules' jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist", "dev": "npm run build:watch", "prepublishOnly": "npm run clean && npm run build", "demo": "npm run build && node demo/main.js", "demo:dev": "tsc && node demo/main.js"}, "keywords": ["ai", "teammates", "framework", "functional", "typescript", "mcp", "tools", "resources", "prompts", "ai-sdlc", "brainstack", "experimental"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.6.0"}, "devDependencies": {"@eslint/js": "^9.30.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.3.3"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "https://github.com/mouimet-infinisoft/AISDLC.git", "directory": "packages/ai-teammates-framework"}}